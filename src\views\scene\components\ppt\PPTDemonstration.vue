<template>
  <div class="ppt-demonstration-container fixed inset-0 z-50 bg-black">
    <!-- 分屏布局 -->
    <div class="flex h-full">
      <!-- 左侧：3D场景 (50%) -->
      <div class="w-1/2 relative">
        <!-- 3D场景容器 -->
        <div ref="sceneContainer" class="w-full h-full relative bg-black">
          <!-- 3D场景将通过JavaScript动态移动到这里 -->
        </div>

        <!-- 3D场景覆盖层 - 显示当前视角信息 -->
        <div class="absolute top-4 left-4 bg-black/70 text-white p-3 rounded backdrop-blur-sm">
          <div class="flex items-center space-x-2">
            <EyeOutlined class="text-blue-400" />
            <span class="text-sm">3D场景视角</span>
          </div>
          <div class="text-xs text-gray-300 mt-1">
            <div v-if="currentViewBinding"> 绑定视角: {{ currentViewBinding.name }} </div>
            <div v-else> 自由视角 (未绑定) </div>
          </div>
        </div>

        <!-- 3D场景控制提示 -->
        <div class="absolute bottom-4 left-4 bg-black/70 text-white p-3 rounded backdrop-blur-sm">
          <div class="text-xs text-gray-300">
            <div>• 鼠标拖拽：旋转视角</div>
            <div>• 滚轮：缩放</div>
            <div>• 右键拖拽：平移</div>
          </div>
        </div>

        <!-- 退出演示按钮 -->
        <div class="absolute top-4 right-4">
          <a-button type="primary" danger @click="exitDemonstration" class="flex items-center">
            <CloseOutlined />
            退出演示
          </a-button>
        </div>
      </div>

      <!-- 分割线 -->
      <div class="w-1 bg-gray-600 relative">
        <div
          class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-6 h-12 bg-gray-500 rounded flex items-center justify-center"
        >
          <div class="w-1 h-8 bg-gray-300 rounded"></div>
        </div>
      </div>

      <!-- 右侧：PPT播放器 (50%) -->
      <div class="w-1/2">
        <SimplePPTPlayer :ppt-path="pptPath" @close="exitDemonstration" @slide-change="handleSlideChange" />
      </div>
    </div>

    <!-- 演示状态栏 -->
    <div class="absolute bottom-0 left-0 right-0 bg-black/90 text-white p-2 flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <div class="flex items-center space-x-2">
          <FileTextOutlined class="text-blue-400" />
          <span class="text-sm font-medium">PPT演示模式</span>
        </div>
        <div class="text-xs text-gray-400"> 左侧：3D场景联动 | 右侧：PPT播放控制 </div>
      </div>

      <div class="flex items-center space-x-4">
        <!-- 同步状态指示器 -->
        <div class="flex items-center space-x-2">
          <div class="w-2 h-2 rounded-full" :class="syncStatus === 'synced' ? 'bg-green-400' : 'bg-yellow-400'"></div>
          <span class="text-xs text-gray-400">
            {{ syncStatus === 'synced' ? '视角已同步' : '视角未绑定' }}
          </span>
        </div>

        <!-- 演示信息 -->
        <div class="text-xs text-gray-400"> 第 {{ currentSlide + 1 }} / {{ totalSlides }} 页 </div>
      </div>
    </div>

    <!-- 快捷键提示 -->
    <div
      v-if="showShortcuts"
      class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-black/90 text-white p-6 rounded-lg backdrop-blur-sm"
    >
      <div class="text-center mb-4">
        <h3 class="text-lg font-medium">快捷键说明</h3>
      </div>
      <div class="space-y-2 text-sm">
        <div class="flex justify-between">
          <span>上一页：</span>
          <span class="text-blue-400">← 或 PageUp</span>
        </div>
        <div class="flex justify-between">
          <span>下一页：</span>
          <span class="text-blue-400">→ 或 PageDown</span>
        </div>
        <div class="flex justify-between">
          <span>自动播放：</span>
          <span class="text-blue-400">Space</span>
        </div>
        <div class="flex justify-between">
          <span>退出演示：</span>
          <span class="text-blue-400">Esc</span>
        </div>
        <div class="flex justify-between">
          <span>显示/隐藏快捷键：</span>
          <span class="text-blue-400">F1</span>
        </div>
      </div>
      <div class="text-center mt-4">
        <a-button size="small" @click="showShortcuts = false"> 关闭 </a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue';
  import { CloseOutlined, EyeOutlined, FileTextOutlined } from '@ant-design/icons-vue';
  import { useGlobalThreeStore } from '../../store/globalThreeStore';
  import SimplePPTPlayer from './SimplePPTPlayer.vue';
  import { SceneManager } from '../../lib/SceneManager';
  import { CameraController } from '../../lib/CameraController';

  // Props
  interface Props {
    pptPath: string;
  }

  const props = defineProps<Props>();

  // Emits
  const emit = defineEmits<{
    exit: [];
  }>();

  // Store
  const globalThreeStore = useGlobalThreeStore();

  // 响应式数据
  const showShortcuts = ref(false);
  const sceneContainer = ref<HTMLElement | null>(null);

  // 计算属性
  const currentSlide = computed(() => globalThreeStore.pptDemonstration.currentSlide);
  const totalSlides = computed(() => globalThreeStore.pptDemonstration.totalSlides);
  const currentViewBinding = computed(() => {
    return globalThreeStore.getPPTViewBinding(currentSlide.value);
  });
  const syncStatus = computed(() => {
    return currentViewBinding.value ? 'synced' : 'unsynced';
  });

  // 方法
  const exitDemonstration = () => {
    // 清理演示状态
    globalThreeStore.setPPTDemonstrationActive(false);
    globalThreeStore.setPPTPlaying(false);

    // 恢复3D场景容器尺寸
    restoreSceneContainer();

    // 触发退出事件
    emit('exit');
  };

  const handleSlideChange = (slideIndex: number) => {
    // PPT页面变化时的处理逻辑已在PPTPlayer中实现
    console.log('Slide changed to:', slideIndex);
  };

  // 调整3D场景容器
  const adjustSceneContainer = async () => {
    await nextTick();

    const sceneManager = SceneManager.getInstance();
    const cameraController = CameraController.getInstance();
    const originalContainer = globalThreeStore.containerRef;

    if (sceneManager && originalContainer && sceneContainer.value) {
      try {
        // 获取原始渲染器
        const renderer = sceneManager.renderer;
        if (renderer) {
          // 保存原始容器引用
          renderer.domElement.setAttribute('data-original-parent', 'true');

          // 将渲染器DOM元素移动到PPT演示的左侧容器
          const rendererDom = renderer.domElement;
          if (rendererDom) {
            sceneContainer.value.appendChild(rendererDom);
          }

          // 调整渲染器尺寸为左侧容器的宽度
          const containerRect = sceneContainer.value.getBoundingClientRect();
          renderer.setSize(containerRect.width, containerRect.height);

          // 更新相机比例
          if (cameraController?.camera) {
            cameraController.camera.aspect = containerRect.width / containerRect.height;
            cameraController.camera.updateProjectionMatrix();
          }

          // 强制渲染
          sceneManager.needsRender = true;
          sceneManager.render();

          console.log('3D场景容器已调整到PPT演示模式，尺寸:', containerRect.width, 'x', containerRect.height);
        }
      } catch (error) {
        console.error('调整3D场景容器失败:', error);
      }
    }
  };

  // 恢复3D场景容器
  const restoreSceneContainer = async () => {
    await nextTick();

    const sceneManager = SceneManager.getInstance();
    const cameraController = CameraController.getInstance();
    const originalContainer = globalThreeStore.containerRef;

    if (sceneManager && originalContainer) {
      try {
        // 获取渲染器
        const renderer = sceneManager.renderer;
        if (renderer) {
          // 将渲染器DOM元素移回原始容器
          const rendererDom = renderer.domElement;
          if (rendererDom && originalContainer) {
            originalContainer.appendChild(rendererDom);
            // 清除标记
            rendererDom.removeAttribute('data-original-parent');
          }

          // 恢复渲染器尺寸
          const containerRect = originalContainer.getBoundingClientRect();
          renderer.setSize(containerRect.width, containerRect.height);

          // 更新相机比例
          if (cameraController?.camera) {
            cameraController.camera.aspect = containerRect.width / containerRect.height;
            cameraController.camera.updateProjectionMatrix();
          }

          // 强制渲染
          sceneManager.needsRender = true;
          sceneManager.render();

          console.log('3D场景容器已恢复到正常模式，尺寸:', containerRect.width, 'x', containerRect.height);
        }
      } catch (error) {
        console.error('恢复3D场景容器失败:', error);
      }
    }
  };

  // 键盘事件处理
  const handleKeyDown = (event: KeyboardEvent) => {
    switch (event.key) {
      case 'Escape':
        exitDemonstration();
        break;
      case 'F1':
        event.preventDefault();
        showShortcuts.value = !showShortcuts.value;
        break;
      case 'ArrowLeft':
      case 'PageUp':
        // 这些事件将由PPTPlayer处理
        break;
      case 'ArrowRight':
      case 'PageDown':
        // 这些事件将由PPTPlayer处理
        break;
      case ' ':
        event.preventDefault();
        // 切换自动播放 - 这个事件将由PPTPlayer处理
        break;
    }
  };

  // 生命周期
  onMounted(async () => {
    // 设置演示状态
    globalThreeStore.setPPTDemonstrationActive(true);

    // 添加键盘事件监听
    document.addEventListener('keydown', handleKeyDown);

    // 调整3D场景容器
    setTimeout(() => {
      adjustSceneContainer();
    }, 100);

    // 显示快捷键提示（3秒后自动隐藏）
    showShortcuts.value = true;
    setTimeout(() => {
      showShortcuts.value = false;
    }, 3000);
  });

  onUnmounted(() => {
    // 移除键盘事件监听
    document.removeEventListener('keydown', handleKeyDown);

    // 清理演示状态
    globalThreeStore.setPPTDemonstrationActive(false);

    // 恢复3D场景容器
    restoreSceneContainer();
  });
</script>

<style scoped>
  .ppt-demonstration-container {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  /* 确保3D场景在左侧正常显示 */
  .ppt-demonstration-container :deep(.scene-container),
  .ppt-demonstration-container :deep(canvas) {
    width: 100% !important;
    height: 100% !important;
    display: block !important;
  }

  /* 隐藏原有的UI元素，只保留3D场景 */
  .ppt-demonstration-container :deep(.bottom-navigation),
  .ppt-demonstration-container :deep(.left-sidebar),
  .ppt-demonstration-container :deep(.right-sidebar) {
    display: none !important;
  }

  /* 确保3D场景的控制器仍然可用 */
  .ppt-demonstration-container :deep(.view-control) {
    display: block !important;
  }
</style>
