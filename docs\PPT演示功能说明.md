# PPT演示功能实现说明

## 功能概述

在3D场景页面的内景模式下，新增了"PPT演示"菜单项，实现了PPT文件在线播放与3D场景视角联动的功能。

## 主要特性

### 1. 布局设计
- **分屏显示**：左侧50%显示3D场景，右侧50%显示PPT播放器
- **响应式布局**：支持不同屏幕尺寸的自适应
- **平滑过渡**：进入/退出演示模式时有平滑的动画效果

### 2. PPT播放功能
- **在线播放**：支持PPTX文件的在线查看
- **导航控制**：上一页/下一页、跳转到指定页面
- **自动播放**：可设置自动播放间隔（3-10秒）
- **进度显示**：显示当前页码和总页数
- **进度条**：可拖拽的进度条快速跳转

### 3. 3D场景联动
- **预设视角**：提供6个预设的3D观察视角
- **视角绑定**：为每个PPT页面绑定特定的3D视角
- **自动切换**：PPT翻页时自动切换到对应的3D视角
- **平滑过渡**：视角切换有1秒的平滑动画

### 4. 视角管理
- **绑定界面**：专门的视角绑定管理界面
- **实时预览**：可预览绑定的视角效果
- **配置导出/导入**：支持视角配置的保存和加载
- **编辑功能**：可编辑已绑定的视角参数

## 技术实现

### 1. 组件结构
```
src/views/scene/components/ppt/
├── PPTDemonstration.vue     # 主演示容器
├── PPTPlayer.vue           # PPT播放器
└── ViewBindingModal.vue    # 视角绑定管理
```

### 2. 状态管理
- 扩展了`globalThreeStore`，添加PPT演示相关状态
- 包含演示状态、当前页面、视角绑定等信息
- 与现有3D功能状态互斥，确保不冲突

### 3. 核心功能
- **PPTPlayer组件**：处理PPT文件显示和播放控制
- **ViewBindingModal组件**：管理视角绑定配置
- **CameraController集成**：利用现有相机控制器实现视角切换

## 使用方法

### 1. 启动演示
1. 切换到3D场景的内景模式
2. 点击底部导航栏的"PPT演示"按钮
3. 系统自动进入分屏演示模式

### 2. 基本操作
- **翻页**：点击PPT两侧的导航箭头或使用键盘方向键
- **跳转**：在控制栏输入页码或拖拽进度条
- **自动播放**：点击"自动播放"按钮，可设置播放间隔

### 3. 视角绑定
1. 点击"视角绑定"按钮打开管理界面
2. 选择预设视角或使用当前3D视角
3. 点击"绑定当前3D视角"完成绑定
4. 绑定后PPT翻页时会自动切换到对应视角

### 4. 快捷键
- `←/→` 或 `PageUp/PageDown`：翻页
- `Space`：切换自动播放
- `Esc`：退出演示模式
- `F1`：显示/隐藏快捷键说明

## 预设视角

系统提供6个预设视角：
1. **全景俯视**：从上方俯视整个场景
2. **正面视角**：从正面观察场景
3. **侧面视角**：从侧面观察场景
4. **斜角视角**：从斜角观察场景（默认视角）
5. **近距离观察**：近距离观察设备细节
6. **设备巡视**：设备巡视视角

## 文件配置

### PPT文件位置
- 默认路径：`docs/副本DCIM平台介绍0527.pptx`
- 支持相对路径和绝对路径
- 建议将PPT文件放在`docs`目录下

### 视角配置
- 支持导出为JSON格式
- 可导入之前保存的配置
- 配置包含相机位置、目标位置、绑定名称等

## 兼容性

### 浏览器支持
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

### 功能限制
- 仅在内景模式下可用
- 与巡检、播放、漫游、透视等功能互斥
- 需要网络连接以加载PPT文件

## 注意事项

1. **性能优化**：演示模式下会自动禁用其他3D功能以确保流畅性
2. **文件大小**：建议PPT文件大小不超过50MB
3. **网络要求**：首次加载需要良好的网络连接
4. **浏览器兼容**：使用Office Online Viewer，需要现代浏览器支持

## 故障排除

### 常见问题
1. **PPT无法加载**：检查文件路径和网络连接
2. **视角切换不流畅**：检查3D场景性能设置
3. **布局显示异常**：刷新页面或检查浏览器兼容性

### 调试方法
- 打开浏览器开发者工具查看控制台错误
- 检查网络请求是否成功
- 验证PPT文件是否可访问

## 扩展功能

### 未来计划
1. 支持更多PPT格式（PDF、图片等）
2. 添加语音解说功能
3. 支持多人协同演示
4. 增加演示录制功能
5. 集成更多3D交互效果

### 自定义开发
- 可扩展预设视角数量
- 可自定义PPT播放器界面
- 可集成第三方PPT服务
- 可添加更多联动效果
